import * as THREE from "three";
import { EARTH_RADIUS } from "../../../../constants";

/**
 * 3D文字标签工具类
 *
 * 用于在3D场景中创建文字标签，支持多种样式和动画效果。
 * 使用Canvas纹理技术在地球表面显示文字。
 *
 * 功能特性：
 * - 支持中文和英文文字渲染
 * - 可自定义字体、大小、颜色
 * - 支持背景色和边框
 * - 自动朝向相机
 * - 支持淡入淡出动画
 * - 自动资源管理
 * - 标签缓存机制，避免重复创建相同位置的标签
 *
 * 使用示例：
 * ```javascript
 * import TextLabel from './TextLabel.js';
 *
 * // 创建简单文字标签
 * const label = TextLabel.createTextLabel(position, "标签", {
 *   fontSize: 32,
 *   color: "#ffffff",
 *   backgroundColor: "rgba(0,0,0,0.7)"
 * });
 * scene.add(label);
 *
 * // 创建带动画的标签
 * TextLabel.fadeInLabel(label, 1000);
 * ```
 */
class TextLabel {
  // 静态标签缓存，用于避免重复创建相同位置的标签
  static labelCache = new Map();

  // 静态活跃标签列表，用于重叠检测
  static activeLabels = new Set();
  /**
   * 创建文字标签（带智能位置检测）
   * @param {THREE.Vector3} position - 标签位置
   * @param {string} text - 显示文字
   * @param {Object} options - 配置选项
   * @param {THREE.Vector3} markerPosition - marker位置（用于计算偏移）
   * @param {boolean} enableCollisionDetection - 是否启用碰撞检测（默认true）
   * @returns {THREE.Mesh} 文字标签网格对象
   */
  static createTextLabel(position, text = "标签", options = {}, markerPosition = null, enableCollisionDetection = true) {
    // 如果启用碰撞检测且提供了marker位置，使用智能位置检测
    if (enableCollisionDetection && markerPosition) {
      return this.createTextLabelWithSmartPositioning(markerPosition, text, options);
    }

    // 否则使用原有逻辑
    // 创建缓存key：文字内容 + 位置坐标（四舍五入到小数点后2位）
    const posKey = `${position.x.toFixed(2)},${position.y.toFixed(2)},${position.z.toFixed(2)}`;
    const cacheKey = `${text}@${posKey}`;

    // 检查缓存中是否已存在相同的标签
    if (this.labelCache.has(cacheKey)) {
      console.log(`标签已存在，跳过创建: ${text} at ${posKey}`);
      return this.labelCache.get(cacheKey);
    }

    const defaultOptions = {
      fontSize: 32,
      fontFamily: "Arial, sans-serif",
      color: "#ffffff",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      borderColor: "#ffffff",
      borderWidth: 2,
      padding: 10,
      borderRadius: 8,
      maxWidth: 200,
      textAlign: "center",
      name: `TextLabel_${Date.now()}`,
      scale: 1.0,
      opacity: 1.0,
      autoFaceCamera: true,
    };

    const config = { ...defaultOptions, ...options };

    // 创建Canvas纹理
    const canvas = this._createTextCanvas(text, config);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 计算标签尺寸（基于Canvas尺寸）
    const aspect = canvas.width / canvas.height;
    const labelWidth = config.scale;
    const labelHeight = labelWidth / aspect;

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(labelWidth, labelHeight);

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: config.opacity,
      side: THREE.DoubleSide,
      alphaTest: 0.1, // 避免透明部分的渲染问题
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置
    mesh.position.copy(position);

    // 设置朝向（朝向相机）
    if (config.autoFaceCamera) {
      this._setLabelOrientation(mesh, position);
    }

    // 设置名称
    mesh.name = config.name;

    // 存储配置和资源引用
    mesh.userData = {
      isTextLabel: true,
      text: text,
      config: config,
      canvas: canvas,
      texture: texture,
      originalPosition: position.clone(),
      cacheKey: cacheKey, // 存储缓存key用于后续清理
    };

    // 将标签添加到缓存
    this.labelCache.set(cacheKey, mesh);
    console.log(`创建新标签: ${text} at ${posKey}`);

    return mesh;
  }

  /**
   * 创建Canvas文字纹理
   * @private
   * @param {string} text - 文字内容
   * @param {Object} config - 配置选项
   * @returns {HTMLCanvasElement} Canvas元素
   */
  static _createTextCanvas(text, config) {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");

    // 设置字体以测量文字尺寸
    context.font = `${config.fontSize}px ${config.fontFamily}`;

    // 测量文字尺寸
    const metrics = context.measureText(text);
    const textWidth = metrics.width;
    // 使用更准确的文字高度计算
    const textHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent || config.fontSize;

    // 计算Canvas尺寸（包含padding）
    const canvasWidth = Math.min(textWidth + config.padding * 2, config.maxWidth + config.padding * 2);
    const canvasHeight = textHeight + config.padding * 2;

    // 设置Canvas尺寸（使用2的幂次方以获得更好的性能）
    canvas.width = this._nextPowerOfTwo(canvasWidth);
    canvas.height = this._nextPowerOfTwo(canvasHeight);

    // 重新设置字体（Canvas尺寸改变后需要重新设置）
    context.font = `${config.fontSize}px ${config.fontFamily}`;
    context.textAlign = "center";
    context.textBaseline = "alphabetic";

    // 清除Canvas
    context.clearRect(0, 0, canvas.width, canvas.height);

    // 计算背景框在Canvas中的位置（居中）
    const bgX = (canvas.width - canvasWidth) / 2;
    const bgY = (canvas.height - canvasHeight) / 2;

    // 绘制背景
    if (config.backgroundColor && config.backgroundColor !== "transparent") {
      context.fillStyle = config.backgroundColor;

      if (config.borderRadius > 0) {
        // 绘制圆角矩形背景
        this._drawRoundedRect(context, bgX, bgY, canvasWidth, canvasHeight, config.borderRadius);
      } else {
        // 绘制普通矩形背景
        context.fillRect(bgX, bgY, canvasWidth, canvasHeight);
      }
    }

    // 绘制边框
    if (config.borderWidth > 0 && config.borderColor) {
      context.strokeStyle = config.borderColor;
      context.lineWidth = config.borderWidth;

      if (config.borderRadius > 0) {
        // 绘制圆角矩形边框
        this._drawRoundedRect(context, bgX, bgY, canvasWidth, canvasHeight, config.borderRadius);
        context.stroke();
      } else {
        // 绘制普通矩形边框
        context.strokeRect(bgX, bgY, canvasWidth, canvasHeight);
      }
    }

    // 绘制文字
    context.fillStyle = config.color;

    // 重新测量文字以获取精确的基线信息
    const textMetrics = context.measureText(text);

    // 文字在背景框中心位置绘制
    const textX = bgX + canvasWidth / 2;
    // 使用alphabetic基线，需要加上ascent来实现垂直居中
    const textY = bgY + canvasHeight / 2 + (textMetrics.actualBoundingBoxAscent || config.fontSize * 0.7) / 2;

    // 处理文字换行（如果文字太长）
    if (textWidth > config.maxWidth) {
      this._drawWrappedText(context, text, textX, textY, config.maxWidth, config.fontSize);
    } else {
      context.fillText(text, textX, textY);
    }

    return canvas;
  }

  /**
   * 绘制圆角矩形
   * @private
   */
  static _drawRoundedRect(context, x, y, width, height, radius) {
    context.beginPath();
    context.moveTo(x + radius, y);
    context.lineTo(x + width - radius, y);
    context.quadraticCurveTo(x + width, y, x + width, y + radius);
    context.lineTo(x + width, y + height - radius);
    context.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    context.lineTo(x + radius, y + height);
    context.quadraticCurveTo(x, y + height, x, y + height - radius);
    context.lineTo(x, y + radius);
    context.quadraticCurveTo(x, y, x + radius, y);
    context.closePath();
    context.fill();
  }

  /**
   * 绘制换行文字
   * @private
   */
  static _drawWrappedText(context, text, x, y, maxWidth, lineHeight) {
    const words = text.split("");
    let line = "";
    let testLine = "";
    let metrics = null;
    let testWidth = 0;

    for (let n = 0; n < words.length; n++) {
      testLine = line + words[n];
      metrics = context.measureText(testLine);
      testWidth = metrics.width;

      if (testWidth > maxWidth && n > 0) {
        context.fillText(line, x, y);
        line = words[n];
        y += lineHeight;
      } else {
        line = testLine;
      }
    }
    context.fillText(line, x, y);
  }

  /**
   * 获取下一个2的幂次方数
   * @private
   */
  static _nextPowerOfTwo(value) {
    return Math.pow(2, Math.ceil(Math.log2(value)));
  }

  /**
   * 设置标签朝向
   * @private
   */
  static _setLabelOrientation(mesh, position) {
    // 计算从地球中心指向标签位置的方向向量
    const direction = position.clone().normalize();

    // 让标签面向相机方向（法向量指向地球中心外侧）
    // 使用 lookAt 让标签的 Z 轴（法向量）指向远离地球中心的方向
    const lookAtTarget = position.clone().add(direction.clone().multiplyScalar(100));
    mesh.lookAt(lookAtTarget);
  }

  /**
   * 更新标签文字
   * @param {THREE.Mesh} label - 标签对象
   * @param {string} newText - 新文字内容
   */
  static updateLabelText(label, newText) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("updateLabelText: 无效的文字标签对象");
      return;
    }

    // 更新文字内容
    label.userData.text = newText;

    // 重新创建Canvas纹理
    const newCanvas = this._createTextCanvas(newText, label.userData.config);
    label.userData.canvas = newCanvas;

    // 更新纹理
    label.userData.texture.image = newCanvas;
    label.userData.texture.needsUpdate = true;

    console.log(`文字标签已更新: ${label.name} -> "${newText}"`);
  }

  /**
   * 淡入显示标签
   * @param {THREE.Mesh} label - 标签对象
   * @param {number} duration - 淡入持续时间（毫秒）
   */
  static fadeInLabel(label, duration = 800) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("fadeInLabel: 无效的文字标签对象");
      return;
    }

    // 停止任何正在进行的动画
    if (label.userData.fadeAnimationId) {
      cancelAnimationFrame(label.userData.fadeAnimationId);
    }

    // 设置初始状态
    label.visible = true;
    const targetOpacity = label.userData.config.opacity || 1.0;
    label.material.opacity = 0.0;

    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeOutCubic缓动函数
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      label.material.opacity = easedProgress * targetOpacity;

      if (progress >= 1) {
        // 动画完成
        label.userData.fadeAnimationId = null;
        console.log(`文字标签淡入动画完成: ${label.name}`);
        return;
      }

      label.userData.fadeAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始文字标签淡入动画: ${label.name}, 持续时间: ${duration}ms`);
    label.userData.fadeAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 淡出隐藏标签
   * @param {THREE.Mesh} label - 标签对象
   * @param {number} duration - 淡出持续时间（毫秒）
   */
  static fadeOutLabel(label, duration = 600) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("fadeOutLabel: 无效的文字标签对象");
      return;
    }

    // 停止任何正在进行的动画
    if (label.userData.fadeAnimationId) {
      cancelAnimationFrame(label.userData.fadeAnimationId);
    }

    // 获取当前透明度
    const startOpacity = label.material.opacity;
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeInCubic缓动函数
      const easedProgress = Math.pow(progress, 3);
      label.material.opacity = startOpacity * (1 - easedProgress);

      if (progress >= 1) {
        // 动画完成，隐藏标签
        label.visible = false;
        label.userData.fadeAnimationId = null;
        console.log(`文字标签淡出动画完成: ${label.name}`);
        return;
      }

      label.userData.fadeAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始文字标签淡出动画: ${label.name}, 持续时间: ${duration}ms`);
    label.userData.fadeAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 销毁文字标签
   * @param {THREE.Mesh} label - 标签对象
   */
  static destroyLabel(label) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("destroyLabel: 无效的文字标签对象");
      return;
    }

    // 停止动画
    if (label.userData.fadeAnimationId) {
      cancelAnimationFrame(label.userData.fadeAnimationId);
    }

    // 从缓存中移除
    if (label.userData.cacheKey) {
      this.labelCache.delete(label.userData.cacheKey);
      console.log(`从缓存中移除标签: ${label.userData.cacheKey}`);
    }

    // 从活跃标签列表中移除
    this.removeFromActiveLabels(label);

    // 清理资源
    if (label.geometry) label.geometry.dispose();
    if (label.material) {
      if (label.material.map) label.material.map.dispose();
      label.material.dispose();
    }

    console.log(`文字标签已销毁: ${label.name}`);
  }

  /**
   * 批量销毁标签
   * @param {Array} labels - 标签对象数组
   */
  static destroyMultipleLabels(labels) {
    if (Array.isArray(labels)) {
      labels.forEach((label) => this.destroyLabel(label));
      console.log(`已销毁 ${labels.length} 个文字标签`);
    }
  }

  /**
   * 清理所有缓存的标签
   */
  static clearLabelCache() {
    const cacheSize = this.labelCache.size;
    this.labelCache.clear();
    this.clearActiveLabels();
    console.log(`已清理 ${cacheSize} 个缓存的标签`);
  }

  /**
   * 获取缓存状态信息
   * @returns {Object} 缓存状态信息
   */
  static getCacheInfo() {
    return {
      size: this.labelCache.size,
      keys: Array.from(this.labelCache.keys()),
    };
  }

  /**
   * 创建带智能位置检测的文字标签
   * @param {THREE.Vector3} markerPosition - marker位置
   * @param {string} text - 显示文字
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 文字标签网格对象
   */
  static createTextLabelWithSmartPositioning(markerPosition, text = "标签", options = {}) {
    const defaultOptions = {
      fontSize: 32,
      fontFamily: "Arial, sans-serif",
      color: "#ffffff",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      borderColor: "#ffffff",
      borderWidth: 2,
      padding: 10,
      borderRadius: 8,
      maxWidth: 200,
      textAlign: "center",
      name: `TextLabel_${Date.now()}`,
      scale: 1.0,
      opacity: 1.0,
      autoFaceCamera: true,
      surfaceOffset: 0.05,
      lateralOffset: 1.5,
      collisionDetectionRadius: 2.0, // 碰撞检测半径
    };

    const config = { ...defaultOptions, ...options };

    // 尝试不同的位置方向，按优先级排序
    const directions = ["right", "left", "up", "down"];
    let bestPosition = null;
    let bestDirection = "right";

    for (const direction of directions) {
      const candidatePosition = this._calculateLabelPosition(markerPosition, direction, config.surfaceOffset, config.lateralOffset);

      // 检查该位置是否与现有标签重叠
      if (!this._checkLabelCollision(candidatePosition, config.collisionDetectionRadius)) {
        bestPosition = candidatePosition;
        bestDirection = direction;
        break;
      }
    }

    // 如果所有方向都有重叠，使用默认的右侧位置
    if (!bestPosition) {
      bestPosition = this._calculateLabelPosition(markerPosition, "right", config.surfaceOffset, config.lateralOffset);
      bestDirection = "right";
      console.warn(`标签位置检测：所有方向都有重叠，使用默认右侧位置: ${text}`);
    }

    console.log(`标签位置检测：选择方向 "${bestDirection}" 用于标签 "${text}"`);

    // 使用原有的createTextLabel逻辑创建标签
    return this._createTextLabelAtPosition(bestPosition, text, config);
  }

  /**
   * 计算标签位置（从FlightLine移植过来）
   * @private
   * @param {THREE.Vector3} markerPosition - marker位置
   * @param {string} direction - 偏移方向
   * @param {number} surfaceOffset - 距离球面的偏移距离
   * @param {number} lateralOffset - 横向偏移量
   * @returns {THREE.Vector3} 计算后的标签位置
   */
  static _calculateLabelPosition(markerPosition, direction = "right", surfaceOffset = 0.1, lateralOffset = 1.5) {
    const markerNormal = markerPosition.clone().normalize();
    const worldUp = new THREE.Vector3(0, 1, 0);

    // 计算切平面上的右方向
    let rightDirection = new THREE.Vector3().crossVectors(worldUp, markerNormal);

    // 处理极点附近的特殊情况
    if (rightDirection.length() < 0.001) {
      rightDirection.set(1, 0, 0);
    } else {
      rightDirection.normalize();
    }

    // 计算切平面上的上方向
    const upDirection = new THREE.Vector3().crossVectors(markerNormal, rightDirection).normalize();

    // 根据方向选择偏移向量
    let offsetDirection;
    switch (direction) {
      case "right":
        offsetDirection = rightDirection;
        break;
      case "left":
        offsetDirection = rightDirection.clone().negate();
        break;
      case "up":
        offsetDirection = upDirection;
        break;
      case "down":
        offsetDirection = upDirection.clone().negate();
        break;
      case "right-up":
        offsetDirection = rightDirection.clone().add(upDirection.clone().multiplyScalar(0.2)).normalize();
        break;
      case "left-up":
        offsetDirection = rightDirection.clone().negate().add(upDirection.clone().multiplyScalar(0.2)).normalize();
        break;
      default:
        offsetDirection = rightDirection;
    }

    // 在切平面上偏移
    const offsetVector = offsetDirection.multiplyScalar(lateralOffset);
    const offsetPosition = markerPosition.clone().add(offsetVector);

    // 投影回球面
    const projectedPosition = offsetPosition.clone().normalize().multiplyScalar(EARTH_RADIUS);

    // 向外推移避免嵌入球面
    const finalPosition = projectedPosition.clone().add(projectedPosition.clone().normalize().multiplyScalar(surfaceOffset));

    return finalPosition;
  }

  /**
   * 检查标签位置是否与现有标签发生碰撞
   * @private
   * @param {THREE.Vector3} position - 待检查的位置
   * @param {number} radius - 碰撞检测半径
   * @returns {boolean} 是否发生碰撞
   */
  static _checkLabelCollision(position, radius) {
    for (const existingLabel of this.activeLabels) {
      if (existingLabel && existingLabel.position) {
        const distance = position.distanceTo(existingLabel.position);
        if (distance < radius) {
          return true; // 发生碰撞
        }
      }
    }
    return false; // 无碰撞
  }

  /**
   * 在指定位置创建文字标签（内部方法）
   * @private
   * @param {THREE.Vector3} position - 标签位置
   * @param {string} text - 显示文字
   * @param {Object} config - 配置选项
   * @returns {THREE.Mesh} 文字标签网格对象
   */
  static _createTextLabelAtPosition(position, text, config) {
    // 创建缓存key
    const posKey = `${position.x.toFixed(2)},${position.y.toFixed(2)},${position.z.toFixed(2)}`;
    const cacheKey = `${text}@${posKey}`;

    // 检查缓存中是否已存在相同的标签
    if (this.labelCache.has(cacheKey)) {
      console.log(`标签已存在，跳过创建: ${text} at ${posKey}`);
      const existingLabel = this.labelCache.get(cacheKey);
      // 确保标签在活跃列表中
      this.activeLabels.add(existingLabel);
      return existingLabel;
    }

    // 创建Canvas纹理
    const canvas = this._createTextCanvas(text, config);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 计算标签尺寸（基于Canvas尺寸）
    const aspect = canvas.width / canvas.height;
    const labelWidth = config.scale;
    const labelHeight = labelWidth / aspect;

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(labelWidth, labelHeight);

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: config.opacity,
      side: THREE.DoubleSide,
      alphaTest: 0.1,
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置
    mesh.position.copy(position);

    // 设置朝向（朝向相机）
    if (config.autoFaceCamera) {
      this._setLabelOrientation(mesh, position);
    }

    // 设置名称
    mesh.name = config.name;

    // 存储配置和资源引用
    mesh.userData = {
      isTextLabel: true,
      text: text,
      config: config,
      canvas: canvas,
      texture: texture,
      originalPosition: position.clone(),
      cacheKey: cacheKey,
    };

    // 将标签添加到缓存和活跃列表
    this.labelCache.set(cacheKey, mesh);
    this.activeLabels.add(mesh);
    console.log(`创建新标签: ${text} at ${posKey}`);

    return mesh;
  }

  /**
   * 从活跃标签列表中移除标签
   * @param {THREE.Mesh} label - 标签对象
   */
  static removeFromActiveLabels(label) {
    if (label && this.activeLabels.has(label)) {
      this.activeLabels.delete(label);
      console.log(`从活跃标签列表中移除: ${label.userData?.text || "unknown"}`);
    }
  }

  /**
   * 清理所有活跃标签
   */
  static clearActiveLabels() {
    const count = this.activeLabels.size;
    this.activeLabels.clear();
    console.log(`已清理 ${count} 个活跃标签`);
  }

  /**
   * 获取活跃标签状态信息
   * @returns {Object} 活跃标签状态信息
   */
  static getActiveLabelsInfo() {
    return {
      size: this.activeLabels.size,
      labels: Array.from(this.activeLabels).map((label) => ({
        text: label.userData?.text || "unknown",
        position: label.position.clone(),
        name: label.name,
      })),
    };
  }
}

export default TextLabel;
